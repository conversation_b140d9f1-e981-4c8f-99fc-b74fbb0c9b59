library(data.tree)
library(dplyr)

# --- 加载并准备数据 ---
file_path <- "C:\\Users\\<USER>\\Desktop\\ee\\R_chembl\\mmc1.csv"
edge_list <- read.csv(file_path, stringsAsFactors = FALSE, header = TRUE)

colnames(edge_list) <- c("child", "parent")

edge_list <- edge_list %>% filter(!is.na(child) & child != "")

# 将空的父节点替换为一个统一的根节点名称。
unified_root_name <- "Chemical_MoA_Classification"
edge_list$parent[is.na(edge_list$parent) | edge_list$parent == ""] <- unified_root_name

# --- 构建树形结构 ---
tree_df <- edge_list %>%
  select(parent, child) %>%
  rename(from = parent, to = child) %>%
  distinct()

# 构建树
chemical_tree <- FromDataFrameNetwork(tree_df)

# --- 导出为Newick格式 ---
asNewick <- function(node) {
  if (node$isLeaf) {
    return(paste0("'", node$name, "'"))
  } else {
    children_newick <- sapply(node$children, asNewick)
    return(paste0("(", paste(children_newick, collapse = ","), ")", "'", node$name, "'"))
  }
}

children_strings <- sapply(chemical_tree$children, asNewick)
newick_string <- paste0("(", paste(children_strings, collapse=","), ");")

# 保存文件
output_file <- "C:\\Users\\<USER>\\Desktop\\ee\\R_chembl\\chemical_classification.tre"
write(newick_string, file = output_file)

# 打印成功信息
cat(paste("分类树已成功保存到文件 '", output_file, "'\n", sep = ""))

GetClassificationPath <- function(tree, leaf_name) {
  node <- FindNode(tree, leaf_name)
  
  if (!is.null(node)) {
    path_vector <- node$path
    path_string <- paste(path_vector, collapse = " -> ")

    cat(paste("药物 '", leaf_name, "' 的分类路径如下:\n", sep = ""))
    cat(path_string, "\n\n")
  } else {
    # 如果在树中没有找到该名称的节点，则给出提示
    cat(paste("错误: 未在分类树中找到药物 '", leaf_name, "'。\n", sep = ""))
    cat("请确保输入的药物名称是一个叶子节点，并且名称完全匹配。\n\n")
  }
}

# 示例 1: 查询药物 "ASPARAGINASE" 的分类路径
input_drug_1 <- "ASPARAGINASE"
GetClassificationPath(chemical_tree, input_drug_1)

# 示例 2: 查询药物 "CYSTEAMINE" 的分类路径
input_drug_2 <- "CYSTEAMINE"
GetClassificationPath(chemical_tree, input_drug_2)

# 示例 3: 查询一个不存在的药物，查看提示信息
input_drug_3 <- "Aspirin"
GetClassificationPath(chemical_tree, input_drug_3)