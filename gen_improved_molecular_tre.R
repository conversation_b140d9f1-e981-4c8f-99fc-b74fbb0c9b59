# 改进的Molecular分类树生成脚本
# 基于更多样化的药物数据构建有意义的molecular分类树

library(data.tree)
library(dplyr)
library(webchem)
library(httr)
library(jsonlite)
library(tidyr)
library(purrr)

cat("生成改进的Molecular分类树\n")

# 使用已验证有效的药物样本
diverse_drugs <- c(
  "CHEMBL25",    # 阿司匹林
  "CHEMBL521",   # 布洛芬
  "CHEMBL112",   # 对乙酰氨基酚
  "CHEMBL154",   # 萘普生
  "CHEMBL1082",  # 阿莫西林
  "CHEMBL6"      # 吲哚美辛
)

cat("分析", length(diverse_drugs), "个多样化药物...\n")

# 收集更全面的molecular数据
all_molecular_data <- data.frame()

for (drug_id in diverse_drugs) {
  cat("查询", drug_id, "...\n")
  
  # 查询药物基本信息
  drug_data <- chembl_query(drug_id, resource = "molecule")
  
  if (length(drug_data) > 0 && !is.null(drug_data[[1]])) {
    drug_info <- drug_data[[1]]
    
    # 添加主要药物信息
    main_drug <- data.frame(
      chembl_id = ifelse(is.null(drug_info$molecule_chembl_id), drug_id, drug_info$molecule_chembl_id),
      pref_name = ifelse(is.null(drug_info$pref_name), paste0("Drug_", drug_id), drug_info$pref_name),
      molecule_type = ifelse(is.null(drug_info$molecule_type), "Unknown", drug_info$molecule_type),
      therapeutic_flag = ifelse(is.null(drug_info$therapeutic_flag), FALSE, drug_info$therapeutic_flag),
      max_phase = ifelse(is.null(drug_info$max_phase), -1, drug_info$max_phase),
      oral = ifelse(is.null(drug_info$oral), FALSE, drug_info$oral),
      indication_class = ifelse(is.null(drug_info$indication_class), "Unknown", drug_info$indication_class),
      stringsAsFactors = FALSE
    )
    
    all_molecular_data <- rbind(all_molecular_data, main_drug)
    
    # 进行有限的亚结构搜索（减少数量以提高质量）
    substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                              "substructure/", drug_id, ".json?limit=20")
    
    response_sub <- GET(substructure_url)
    
    if (http_status(response_sub)$category == "Success") {
      sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"), 
                           flatten = TRUE)
      
      if (!is.null(sub_list$molecules) && length(sub_list$molecules) > 0) {
        sub_data <- as_tibble(sub_list$molecules) %>%
          select(chembl_id = molecule_chembl_id, pref_name, 
                 molecule_type, therapeutic_flag, max_phase, oral, indication_class) %>%
          filter(!is.na(pref_name), pref_name != "", chembl_id != drug_id) %>%
          head(5)  # 只取前5个相似化合物
        
        if (nrow(sub_data) > 0) {
          all_molecular_data <- rbind(all_molecular_data, sub_data)
        }
      }
    }
  }
  
  Sys.sleep(0.5)  # 避免API请求过快
}

# 数据清理和去重
all_molecular_data <- all_molecular_data %>% 
  distinct() %>%
  filter(!is.na(pref_name), pref_name != "")

cat("收集到", nrow(all_molecular_data), "个化合物的molecular数据\n")

# 构建更有意义的Molecular层级结构
cat("构建Molecular分类层级...\n")

edge_list <- data.frame(child = character(), parent = character(), stringsAsFactors = FALSE)

for (i in 1:nrow(all_molecular_data)) {
  row <- all_molecular_data[i, ]
  
  # 数据预处理
  mol_type <- ifelse(is.na(row$molecule_type) | row$molecule_type == "", 
                     "Unknown_Type", gsub(" ", "_", row$molecule_type))
  
  therapeutic <- ifelse(is.na(row$therapeutic_flag) | row$therapeutic_flag == "", 
                       "Unknown_Therapeutic", 
                       ifelse(row$therapeutic_flag, "Therapeutic_Drug", "Research_Compound"))
  
  phase_num <- ifelse(is.na(row$max_phase), -999, row$max_phase)
  phase <- case_when(
    phase_num == 4 ~ "Approved_Phase4",
    phase_num == 3 ~ "Phase3_Clinical",
    phase_num == 2 ~ "Phase2_Clinical", 
    phase_num == 1 ~ "Phase1_Clinical",
    phase_num == 0 ~ "Preclinical_Phase0",
    phase_num == -1 ~ "Research_Stage",
    TRUE ~ "Unknown_Phase"
  )
  
  route <- ifelse(is.na(row$oral) | row$oral == "", 
                 "Unknown_Route", 
                 ifelse(row$oral, "Oral_Administration", "Non_Oral_Administration"))
  
  # 简化适应症分类
  indication <- ifelse(is.na(row$indication_class) | row$indication_class == "" | row$indication_class == "Unknown", 
                      "Unknown_Indication", 
                      case_when(
                        grepl("Analgesic|Pain", row$indication_class, ignore.case = TRUE) ~ "Analgesic_Drugs",
                        grepl("Anti-inflammatory|Inflammatory", row$indication_class, ignore.case = TRUE) ~ "Anti_Inflammatory_Drugs",
                        grepl("Antibiotic|Antimicrobial", row$indication_class, ignore.case = TRUE) ~ "Antimicrobial_Drugs",
                        grepl("Cardiovascular|Cardiac|Heart", row$indication_class, ignore.case = TRUE) ~ "Cardiovascular_Drugs",
                        grepl("Neurological|Nervous|CNS", row$indication_class, ignore.case = TRUE) ~ "Neurological_Drugs",
                        TRUE ~ "Other_Therapeutic_Areas"
                      ))
  
  drug_name <- paste0(row$pref_name, "_", row$chembl_id)
  
  # 构建层级关系：Drug -> Indication -> Route -> Phase -> Therapeutic -> MolType -> Root
  
  # Drug -> Indication
  edge_list <- rbind(edge_list, data.frame(
    child = drug_name,
    parent = indication,
    stringsAsFactors = FALSE
  ))
  
  # Indication -> Route
  edge_list <- rbind(edge_list, data.frame(
    child = indication,
    parent = route,
    stringsAsFactors = FALSE
  ))
  
  # Route -> Phase
  edge_list <- rbind(edge_list, data.frame(
    child = route,
    parent = phase,
    stringsAsFactors = FALSE
  ))
  
  # Phase -> Therapeutic
  edge_list <- rbind(edge_list, data.frame(
    child = phase,
    parent = therapeutic,
    stringsAsFactors = FALSE
  ))
  
  # Therapeutic -> MolType
  edge_list <- rbind(edge_list, data.frame(
    child = therapeutic,
    parent = mol_type,
    stringsAsFactors = FALSE
  ))
  
  # MolType -> Root
  edge_list <- rbind(edge_list, data.frame(
    child = mol_type,
    parent = "Molecular_Classification_Root",
    stringsAsFactors = FALSE
  ))
}

# 去重
edge_list <- edge_list %>% distinct()

cat("生成", nrow(edge_list), "条边关系\n")

# 构建树
tree_df <- edge_list %>%
  select(parent, child) %>%
  rename(from = parent, to = child) %>%
  distinct()

molecular_tree <- FromDataFrameNetwork(tree_df)

# 生成Newick格式
asNewick <- function(node) {
  if (node$isLeaf) {
    return(paste0("'", gsub("'", "''", node$name), "'"))
  } else {
    children_newick <- sapply(node$children, asNewick)
    return(paste0("(", paste(children_newick, collapse = ","), ")", "'", gsub("'", "''", node$name), "'"))
  }
}

children_strings <- sapply(molecular_tree$children, asNewick)
newick_string <- paste0("(", paste(children_strings, collapse = ","), ");")

# 保存文件
output_file <- "improved_molecular_classification.tre"
write(newick_string, file = output_file)

cat("改进的Molecular分类树已保存到:", output_file, "\n")

# 创建查询函数
GetMolecularPath <- function(tree, drug_name) {
  found_nodes <- Traverse(tree, filterFun = function(node) {
    grepl(drug_name, node$name, ignore.case = TRUE)
  })
  
  if (length(found_nodes) > 0) {
    for (node in found_nodes) {
      path_vector <- node$path
      path_string <- paste(path_vector, collapse = " -> ")
      
      cat(paste("药物 '", drug_name, "' 的Molecular分类路径:\n", sep = ""))
      cat(path_string, "\n\n")
    }
  } else {
    cat(paste("未在Molecular分类树中找到药物 '", drug_name, "'\n", sep = ""))
  }
}

# 示例查询
cat("\n=== Molecular分类路径查询示例 ===\n")
GetMolecularPath(molecular_tree, "ASPIRIN")
GetMolecularPath(molecular_tree, "IBUPROFEN")
GetMolecularPath(molecular_tree, "AMOXICILLIN")

cat("\n改进的Molecular分类树生成完成！\n")
cat("树的层级结构: 根节点 -> 分子类型 -> 治疗/研究状态 -> 临床阶段 -> 给药途径 -> 适应症 -> 具体药物\n")
