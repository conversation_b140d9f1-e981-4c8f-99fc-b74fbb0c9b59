# 基于ATC分类生成分类树
library(data.tree)
library(dplyr)
library(webchem)
library(httr)
library(jsonlite)
library(tidyr)
library(purrr)

cat("生成ATC分类树\n")

# 获取ATC分类数据
cat("获取ATC分类数据...\n")
atc_data <- chembl_atc_classes()

if (length(atc_data) == 0) {
  stop("无法获取ATC分类数据")
}

cat("获取", nrow(atc_data), "条ATC分类记录\n")

# 构建ATC层级结构的边列表
cat("构建ATC层级结构...\n")

# 创建边列表 (child -> parent)
edge_list <- data.frame(child = character(), parent = character(), stringsAsFactors = FALSE)

# 为每个ATC记录创建层级关系
for (i in 1:nrow(atc_data)) {
  row <- atc_data[i, ]
  
  # Level 5 -> Level 4
  if (!is.na(row$level5) && !is.na(row$level4)) {
    edge_list <- rbind(edge_list, data.frame(
      child = paste0(row$level5, " (", row$who_name, ")"),
      parent = paste0(row$level4, " - ", row$level4_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 4 -> Level 3
  if (!is.na(row$level4) && !is.na(row$level3)) {
    edge_list <- rbind(edge_list, data.frame(
      child = paste0(row$level4, " - ", row$level4_description),
      parent = paste0(row$level3, " - ", row$level3_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 3 -> Level 2
  if (!is.na(row$level3) && !is.na(row$level2)) {
    edge_list <- rbind(edge_list, data.frame(
      child = paste0(row$level3, " - ", row$level3_description),
      parent = paste0(row$level2, " - ", row$level2_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 2 -> Level 1
  if (!is.na(row$level2) && !is.na(row$level1)) {
    edge_list <- rbind(edge_list, data.frame(
      child = paste0(row$level2, " - ", row$level2_description),
      parent = paste0(row$level1, " - ", row$level1_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 1 -> Root
  if (!is.na(row$level1)) {
    edge_list <- rbind(edge_list, data.frame(
      child = paste0(row$level1, " - ", row$level1_description),
      parent = "ATC_Classification_Root",
      stringsAsFactors = FALSE
    ))
  }
}

# 去重
edge_list <- edge_list %>% distinct()

cat("生成", nrow(edge_list), "条边关系\n")

# 构建树形结构
cat("构建ATC分类树...\n")
tree_df <- edge_list %>%
  select(parent, child) %>%
  rename(from = parent, to = child) %>%
  distinct()

# 构建树
atc_tree <- FromDataFrameNetwork(tree_df)

# 导出为Newick格式的函数
asNewick <- function(node) {
  if (node$isLeaf) {
    return(paste0("'", gsub("'", "''", node$name), "'"))
  } else {
    children_newick <- sapply(node$children, asNewick)
    return(paste0("(", paste(children_newick, collapse = ","), ")", "'", gsub("'", "''", node$name), "'"))
  }
}

# 生成Newick字符串
children_strings <- sapply(atc_tree$children, asNewick)
newick_string <- paste0("(", paste(children_strings, collapse = ","), ");")

# 保存ATC分类树文件
output_file <- "atc_classification.tre"
write(newick_string, file = output_file)

cat("ATC分类树已保存到:", output_file, "\n")

# 创建查询函数
GetATCClassificationPath <- function(tree, drug_name) {
  # 在树中查找包含药物名称的节点
  found_nodes <- Traverse(tree, filterFun = function(node) {
    grepl(drug_name, node$name, ignore.case = TRUE)
  })
  
  if (length(found_nodes) > 0) {
    for (node in found_nodes) {
      path_vector <- node$path
      path_string <- paste(path_vector, collapse = " -> ")
      
      cat(paste("药物 '", drug_name, "' 的ATC分类路径:\n", sep = ""))
      cat(path_string, "\n\n")
    }
  } else {
    cat(paste("未在ATC分类树中找到药物 '", drug_name, "'\n", sep = ""))
  }
}

# 示例查询
cat("\n=== ATC分类路径查询示例 ===\n")

# 查询阿司匹林相关的ATC分类
GetATCClassificationPath(atc_tree, "acetylsalicylic acid")

# 查询布洛芬相关的ATC分类
GetATCClassificationPath(atc_tree, "ibuprofen")

cat("ATC分类树生成完成！\n")
