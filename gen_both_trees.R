# 生成ATC和Molecular两种分类树
# 用法: Rscript gen_both_trees.R

cat("=== ChEMBL分类树生成工具 ===\n")
cat("将生成ATC分类树和Molecular分类树\n\n")

# 检查并安装必要的包
required_packages <- c("data.tree", "dplyr", "webchem", "httr", "jsonlite", "tidyr", "purrr")

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    cat("安装", pkg, "包...\n")
    install.packages(pkg, quiet = TRUE)
  }
}

# 加载库
suppressPackageStartupMessages({
  library(data.tree)
  library(dplyr)
  library(webchem)
  library(httr)
  library(jsonlite)
  library(tidyr)
  library(purrr)
})

# ============================================================================
# 第一部分：生成ATC分类树
# ============================================================================
cat("1. 生成ATC分类树\n")

# 获取ATC分类数据
cat("获取ATC分类数据...\n")
atc_data <- chembl_atc_classes()

if (length(atc_data) == 0) {
  stop("无法获取ATC分类数据")
}

cat("获取", nrow(atc_data), "条ATC分类记录\n")

# 构建ATC层级结构的边列表
atc_edge_list <- data.frame(child = character(), parent = character(), stringsAsFactors = FALSE)

for (i in 1:nrow(atc_data)) {
  row <- atc_data[i, ]
  
  # Level 5 -> Level 4
  if (!is.na(row$level5) && !is.na(row$level4)) {
    atc_edge_list <- rbind(atc_edge_list, data.frame(
      child = paste0(row$level5, " (", row$who_name, ")"),
      parent = paste0(row$level4, " - ", row$level4_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 4 -> Level 3
  if (!is.na(row$level4) && !is.na(row$level3)) {
    atc_edge_list <- rbind(atc_edge_list, data.frame(
      child = paste0(row$level4, " - ", row$level4_description),
      parent = paste0(row$level3, " - ", row$level3_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 3 -> Level 2
  if (!is.na(row$level3) && !is.na(row$level2)) {
    atc_edge_list <- rbind(atc_edge_list, data.frame(
      child = paste0(row$level3, " - ", row$level3_description),
      parent = paste0(row$level2, " - ", row$level2_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 2 -> Level 1
  if (!is.na(row$level2) && !is.na(row$level1)) {
    atc_edge_list <- rbind(atc_edge_list, data.frame(
      child = paste0(row$level2, " - ", row$level2_description),
      parent = paste0(row$level1, " - ", row$level1_description),
      stringsAsFactors = FALSE
    ))
  }
  
  # Level 1 -> Root
  if (!is.na(row$level1)) {
    atc_edge_list <- rbind(atc_edge_list, data.frame(
      child = paste0(row$level1, " - ", row$level1_description),
      parent = "ATC_Classification_Root",
      stringsAsFactors = FALSE
    ))
  }
}

# 去重并构建ATC树
atc_edge_list <- atc_edge_list %>% distinct()
atc_tree_df <- atc_edge_list %>%
  select(parent, child) %>%
  rename(from = parent, to = child) %>%
  distinct()

atc_tree <- FromDataFrameNetwork(atc_tree_df)

# 导出为Newick格式
asNewick <- function(node) {
  if (node$isLeaf) {
    return(paste0("'", gsub("'", "''", node$name), "'"))
  } else {
    children_newick <- sapply(node$children, asNewick)
    return(paste0("(", paste(children_newick, collapse = ","), ")", "'", gsub("'", "''", node$name), "'"))
  }
}

children_strings <- sapply(atc_tree$children, asNewick)
atc_newick <- paste0("(", paste(children_strings, collapse = ","), ");")

# 保存ATC分类树
write(atc_newick, file = "atc_classification.tre")
cat("ATC分类树已保存到: atc_classification.tre\n")

# ============================================================================
# 第二部分：生成Molecular分类树
# ============================================================================
cat("\n2. 生成Molecular分类树\n")

# 获取代表性药物数据
representative_drugs <- c("CHEMBL25", "CHEMBL521", "CHEMBL112", "CHEMBL1200766", 
                         "CHEMBL154", "CHEMBL1082", "CHEMBL6")

cat("分析", length(representative_drugs), "个代表性药物...\n")

molecular_data <- data.frame()

for (drug_id in representative_drugs) {
  drug_data <- chembl_query(drug_id, resource = "molecule")
  
  if (length(drug_data) > 0 && !is.null(drug_data[[1]])) {
    substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                              "substructure/", drug_id, ".json?limit=30")
    
    response_sub <- GET(substructure_url)
    
    if (http_status(response_sub)$category == "Success") {
      sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"), 
                           flatten = TRUE)
      
      if (!is.null(sub_list$molecules) && length(sub_list$molecules) > 0) {
        sub_data <- as_tibble(sub_list$molecules) %>%
          select(chembl_id = molecule_chembl_id, pref_name, 
                 molecule_type, therapeutic_flag, max_phase, oral, indication_class) %>%
          filter(!is.na(pref_name))
        
        molecular_data <- rbind(molecular_data, sub_data)
      }
    }
  }
  
  Sys.sleep(0.3)
}

molecular_data <- molecular_data %>% distinct()
cat("收集到", nrow(molecular_data), "个化合物数据\n")

# 构建Molecular层级结构
mol_edge_list <- data.frame(child = character(), parent = character(), stringsAsFactors = FALSE)

for (i in 1:nrow(molecular_data)) {
  row <- molecular_data[i, ]
  
  mol_type <- ifelse(is.na(row$molecule_type) | row$molecule_type == "", "Unknown_Type", row$molecule_type)
  therapeutic <- ifelse(is.na(row$therapeutic_flag) | row$therapeutic_flag == "", "Unknown_Therapeutic", 
                       ifelse(row$therapeutic_flag, "Therapeutic", "Non_Therapeutic"))
  phase <- ifelse(is.na(row$max_phase), "Unknown_Phase", paste0("Phase_", row$max_phase))
  
  drug_name <- paste0(row$pref_name, " (", row$chembl_id, ")")
  
  # Drug -> Therapeutic
  mol_edge_list <- rbind(mol_edge_list, data.frame(
    child = drug_name,
    parent = paste0("Therapeutic_", therapeutic),
    stringsAsFactors = FALSE
  ))
  
  # Therapeutic -> Phase
  mol_edge_list <- rbind(mol_edge_list, data.frame(
    child = paste0("Therapeutic_", therapeutic),
    parent = paste0("Clinical_", phase),
    stringsAsFactors = FALSE
  ))
  
  # Phase -> Molecule Type
  mol_edge_list <- rbind(mol_edge_list, data.frame(
    child = paste0("Clinical_", phase),
    parent = paste0("MolType_", mol_type),
    stringsAsFactors = FALSE
  ))
  
  # Molecule Type -> Root
  mol_edge_list <- rbind(mol_edge_list, data.frame(
    child = paste0("MolType_", mol_type),
    parent = "Molecular_Classification_Root",
    stringsAsFactors = FALSE
  ))
}

# 去重并构建Molecular树
mol_edge_list <- mol_edge_list %>% distinct()
mol_tree_df <- mol_edge_list %>%
  select(parent, child) %>%
  rename(from = parent, to = child) %>%
  distinct()

molecular_tree <- FromDataFrameNetwork(mol_tree_df)

# 生成Molecular树的Newick格式
mol_children_strings <- sapply(molecular_tree$children, asNewick)
mol_newick <- paste0("(", paste(mol_children_strings, collapse = ","), ");")

# 保存Molecular分类树
write(mol_newick, file = "molecular_classification.tre")
cat("Molecular分类树已保存到: molecular_classification.tre\n")

# ============================================================================
# 总结
# ============================================================================
cat("\n=== 分类树生成完成 ===\n")
cat("生成的文件:\n")
cat("- atc_classification.tre (", nrow(atc_edge_list), "条边关系)\n")
cat("- molecular_classification.tre (", nrow(mol_edge_list), "条边关系)\n")
cat("\n两个分类树文件已成功生成！\n")
