# ==============================================================================
# ChEMBL药物查询和ATC分类分析工具 (通用版)
# 用法: Rscript chembl_drug_analyzer.R [ChEMBL_ID]
# 示例: Rscript chembl_drug_analyzer.R CHEMBL25
# ==============================================================================

# 获取命令行参数
args <- commandArgs(trailingOnly = TRUE)

# 设置目标药物
if (length(args) > 0) {
  target_drug <- args[1]
} else {
  target_drug <- "CHEMBL25"  # 默认使用阿司匹林
}

cat("ChEMBL药物分析工具\n")
cat("目标药物:", target_drug, "\n\n")

# 检查并安装必要的包
required_packages <- c("webchem", "httr", "jsonlite", "dplyr", 
                      "tidyr", "purrr", "knitr")

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg, quiet = TRUE)
  }
}

# 加载库
suppressPackageStartupMessages({
  library(webchem)
  library(httr)
  library(jsonlite)
  library(dplyr)
  library(tidyr)
  library(purrr)
  library(knitr)
})

# 1. 查询药物基本信息
cat("1. 查询药物信息\n")
drug_data <- chembl_query(target_drug, resource = "molecule")

if (length(drug_data) > 0 && !is.null(drug_data[[1]])) {
  drug_info <- drug_data[[1]]
  
  cat("ChEMBL ID:", drug_info$molecule_chembl_id, "\n")
  drug_name <- ifelse(is.null(drug_info$pref_name), "未命名", drug_info$pref_name)
  cat("药物名称:", drug_name, "\n")
  cat("分子式:", drug_info$molecule_properties$full_molformula, "\n")
  cat("分子量:", drug_info$molecule_properties$full_mwt, "\n")
  
  if (length(drug_info$atc_classifications) > 0) {
    cat("ATC代码:", paste(drug_info$atc_classifications, collapse = ", "), "\n")
  } else {
    cat("ATC代码: 无\n")
  }
} else {
  stop(paste("无法获取药物信息:", target_drug))
}

cat("\n")

# 2. 获取ATC分类数据
cat("2. 获取ATC分类数据\n")
atc_data <- chembl_atc_classes()

if (length(atc_data) > 0) {
  cat("获取", nrow(atc_data), "条ATC分类记录\n")
} else {
  stop("无法获取ATC分类数据")
}

cat("\n")

# 3. 亚结构搜索
cat("3. 亚结构搜索\n")
substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                          "substructure/", target_drug, ".json?limit=200")

response_sub <- GET(substructure_url)

if (http_status(response_sub)$category == "Success") {
  sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"), 
                       flatten = TRUE)
  
  if (!is.null(sub_list$molecules) && length(sub_list$molecules) > 0) {
    cat("找到", length(sub_list$molecules), "个相似化合物\n")
    
    substructure_data <- as_tibble(sub_list$molecules) %>%
      select(chembl_id = molecule_chembl_id, pref_name, atc_classifications)
  } else {
    cat("亚结构搜索无结果\n")
    quit(save = "no", status = 0)
  }
} else {
  cat("亚结构搜索失败\n")
  quit(save = "no", status = 1)
}

cat("\n")

# 4. ATC分类分析
cat("4. ATC分类分析\n")

# 提取ATC代码
extract_atc_codes <- function(atc_classifications) {
  if (is.character(atc_classifications) && length(atc_classifications) > 0) {
    atc_classifications
  } else {
    NULL
  }
}

molecules_with_atc <- substructure_data %>%
  mutate(atc_codes = map(atc_classifications, extract_atc_codes)) %>%
  filter(!map_lgl(atc_codes, is.null)) %>%
  unnest(atc_codes) %>%
  rename(atc_code = atc_codes)

if (nrow(molecules_with_atc) > 0) {
  final_results <- molecules_with_atc %>%
    left_join(atc_data %>% rename(atc_code = level5), by = "atc_code") %>%
    filter(!is.na(who_name))
  
  if (nrow(final_results) > 0) {
    cat("匹配", nrow(final_results), "条ATC记录\n\n")
    
    # 目标药物ATC分类
    target_atc <- final_results %>% filter(chembl_id == target_drug)
    
    if (nrow(target_atc) > 0) {
      cat(paste0(drug_name, "的ATC分类:\n"))
      target_table <- target_atc %>%
        select(ATC代码 = atc_code, WHO名称 = who_name,
               四级分类 = level4_description, 一级分类 = level1_description)
      
      print(kable(target_table))
    }
    
    # 其他相似化合物
    other_compounds <- final_results %>%
      filter(chembl_id != target_drug) %>%
      select(chembl_id, pref_name, atc_code, who_name, 
             level4_description, level1_description) %>%
      distinct() %>%
      mutate(pref_name = ifelse(is.na(pref_name), "未命名", pref_name))
    
    if (nrow(other_compounds) > 0) {
      cat("\n相似化合物ATC分类:\n")
      other_table <- other_compounds %>%
        select(ChEMBL_ID = chembl_id, 化合物名称 = pref_name,
               ATC代码 = atc_code, WHO名称 = who_name)
      
      print(kable(other_table))
      
      # 统计
      cat("\n按ATC一级分类统计:\n")
      atc_summary <- other_compounds %>%
        filter(!is.na(level1_description)) %>%
        group_by(level1_description) %>%
        summarise(
          化合物数量 = n(),
          代表性化合物 = paste(head(unique(pref_name), 3), collapse = "; "),
          .groups = "drop"
        ) %>%
        arrange(desc(化合物数量))
      
      print(kable(atc_summary))
    } else {
      cat("未找到其他具有ATC分类的相似化合物\n")
    }
    
    # 总结
    cat("\n分析完成\n")
    total_compounds <- length(unique(final_results$chembl_id))
    total_atc_codes <- length(unique(final_results$atc_code))
    cat("分析了", total_compounds, "个化合物,", total_atc_codes, "个ATC代码\n")
    
  } else {
    cat("未匹配到ATC分类信息\n")
  }
} else {
  cat("搜索结果中无ATC分类\n")
}
