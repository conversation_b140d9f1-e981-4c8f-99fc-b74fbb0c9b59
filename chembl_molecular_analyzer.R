# ChEMBL药物Molecular分类分析工具
# 用法: Rscript chembl_molecular_analyzer.R [ChEMBL_ID]
# 示例: Rscript chembl_molecular_analyzer.R CHEMBL25

# 获取命令行参数
args <- commandArgs(trailingOnly = TRUE)

# 设置目标药物
if (length(args) > 0) {
  target_drug <- args[1]
} else {
  target_drug <- "CHEMBL25"  # 默认使用阿司匹林
}

cat("ChEMBL药物Molecular分类分析工具\n")
cat("目标药物:", target_drug, "\n\n")

# 检查并安装必要的包
required_packages <- c("webchem", "httr", "jsonlite", "dplyr", 
                      "tidyr", "purrr", "knitr")

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg, quiet = TRUE)
  }
}

# 加载库
suppressPackageStartupMessages({
  library(webchem)
  library(httr)
  library(jsonlite)
  library(dplyr)
  library(tidyr)
  library(purrr)
  library(knitr)
})

# 1. 查询药物基本信息
cat("1. 查询药物信息\n")
drug_data <- chembl_query(target_drug, resource = "molecule")

if (length(drug_data) > 0 && !is.null(drug_data[[1]])) {
  drug_info <- drug_data[[1]]
  
  cat("ChEMBL ID:", drug_info$molecule_chembl_id, "\n")
  drug_name <- ifelse(is.null(drug_info$pref_name), "未命名", drug_info$pref_name)
  cat("药物名称:", drug_name, "\n")
  cat("分子式:", drug_info$molecule_properties$full_molformula, "\n")
  cat("分子量:", drug_info$molecule_properties$full_mwt, "\n")
  
  # Molecular分类信息
  cat("分子类型:", ifelse(is.null(drug_info$molecule_type), "未知", drug_info$molecule_type), "\n")
  cat("治疗标志:", ifelse(is.null(drug_info$therapeutic_flag) || drug_info$therapeutic_flag == "", "未知", 
                      ifelse(drug_info$therapeutic_flag, "是", "否")), "\n")
  cat("最大临床阶段:", ifelse(is.null(drug_info$max_phase), "未知", drug_info$max_phase), "\n")
  cat("口服给药:", ifelse(is.null(drug_info$oral) || drug_info$oral == "", "未知", 
                    ifelse(drug_info$oral, "是", "否")), "\n")
  
  if (!is.null(drug_info$indication_class) && drug_info$indication_class != "") {
    cat("适应症分类:", drug_info$indication_class, "\n")
  }
  
  if (length(drug_info$atc_classifications) > 0) {
    cat("ATC代码:", paste(drug_info$atc_classifications, collapse = ", "), "\n")
  } else {
    cat("ATC代码: 无\n")
  }
} else {
  stop(paste("无法获取药物信息:", target_drug))
}

cat("\n")

# 2. 获取ATC分类数据（用于后续关联）
cat("2. 获取ATC分类数据\n")
atc_data <- chembl_atc_classes()

if (length(atc_data) > 0) {
  cat("获取", nrow(atc_data), "条ATC分类记录\n")
} else {
  stop("无法获取ATC分类数据")
}

cat("\n")

# 3. 亚结构搜索
cat("3. 亚结构搜索\n")
substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                          "substructure/", target_drug, ".json?limit=200")

response_sub <- GET(substructure_url)

if (http_status(response_sub)$category == "Success") {
  sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"), 
                       flatten = TRUE)
  
  if (!is.null(sub_list$molecules) && length(sub_list$molecules) > 0) {
    cat("找到", length(sub_list$molecules), "个相似化合物\n")
    
    substructure_data <- as_tibble(sub_list$molecules) %>%
      select(chembl_id = molecule_chembl_id, pref_name, atc_classifications,
             molecule_type, therapeutic_flag, max_phase, oral, indication_class)
  } else {
    cat("亚结构搜索无结果\n")
    quit(save = "no", status = 0)
  }
} else {
  cat("亚结构搜索失败\n")
  quit(save = "no", status = 1)
}

cat("\n")

# 4. Molecular分类分析
cat("4. Molecular分类分析\n")

# 目标药物的Molecular信息
cat(paste0(drug_name, "的Molecular分类:\n"))
target_molecular <- data.frame(
  属性 = c("分子类型", "治疗标志", "最大临床阶段", "口服给药", "适应症分类"),
  值 = c(
    ifelse(is.null(drug_info$molecule_type), "未知", drug_info$molecule_type),
    ifelse(is.null(drug_info$therapeutic_flag) || drug_info$therapeutic_flag == "", "未知", 
           ifelse(drug_info$therapeutic_flag, "是", "否")),
    ifelse(is.null(drug_info$max_phase), "未知", drug_info$max_phase),
    ifelse(is.null(drug_info$oral) || drug_info$oral == "", "未知", 
           ifelse(drug_info$oral, "是", "否")),
    ifelse(is.null(drug_info$indication_class) || drug_info$indication_class == "", "未知", drug_info$indication_class)
  ),
  stringsAsFactors = FALSE
)

print(kable(target_molecular))

# 相似化合物的Molecular分类
other_molecular <- substructure_data %>%
  filter(chembl_id != target_drug, !is.na(pref_name)) %>%
  mutate(
    pref_name = ifelse(is.na(pref_name), "未命名", pref_name),
    molecule_type = ifelse(is.na(molecule_type) | molecule_type == "", "未知", molecule_type),
    therapeutic_flag = ifelse(is.na(therapeutic_flag) | therapeutic_flag == "", "未知", 
                             ifelse(therapeutic_flag, "是", "否")),
    max_phase = ifelse(is.na(max_phase), "未知", max_phase),
    oral = ifelse(is.na(oral) | oral == "", "未知", ifelse(oral, "是", "否"))
  ) %>%
  select(ChEMBL_ID = chembl_id, 化合物名称 = pref_name, 
         分子类型 = molecule_type, 治疗标志 = therapeutic_flag,
         最大临床阶段 = max_phase, 口服给药 = oral)

if (nrow(other_molecular) > 0) {
  cat("\n相似化合物Molecular分类:\n")
  print(kable(head(other_molecular, 15)))
  
  # 按分子类型统计
  cat("\n按分子类型统计:\n")
  mol_type_summary <- substructure_data %>%
    filter(!is.na(pref_name)) %>%
    mutate(
      mol_type = ifelse(is.na(molecule_type) | molecule_type == "", "未知", molecule_type),
      pref_name = ifelse(is.na(pref_name), "未命名", pref_name)
    ) %>%
    group_by(mol_type) %>%
    summarise(
      化合物数量 = n(),
      代表性化合物 = paste(head(unique(pref_name), 3), collapse = "; "),
      .groups = "drop"
    ) %>%
    arrange(desc(化合物数量))
  
  print(kable(mol_type_summary))
  
  # 按治疗标志统计
  cat("\n按治疗标志统计:\n")
  therapeutic_summary <- substructure_data %>%
    filter(!is.na(pref_name)) %>%
    mutate(
      therapeutic = ifelse(is.na(therapeutic_flag) | therapeutic_flag == "", "未知", 
                          ifelse(therapeutic_flag, "治疗用", "非治疗用")),
      pref_name = ifelse(is.na(pref_name), "未命名", pref_name)
    ) %>%
    group_by(therapeutic) %>%
    summarise(
      化合物数量 = n(),
      代表性化合物 = paste(head(unique(pref_name), 3), collapse = "; "),
      .groups = "drop"
    ) %>%
    arrange(desc(化合物数量))
  
  print(kable(therapeutic_summary))
  
  # 按临床阶段统计
  cat("\n按最大临床阶段统计:\n")
  phase_summary <- substructure_data %>%
    filter(!is.na(pref_name), !is.na(max_phase)) %>%
    mutate(
      phase = ifelse(is.na(max_phase), "未知", paste0("Phase ", max_phase)),
      pref_name = ifelse(is.na(pref_name), "未命名", pref_name)
    ) %>%
    group_by(phase) %>%
    summarise(
      化合物数量 = n(),
      代表性化合物 = paste(head(unique(pref_name), 3), collapse = "; "),
      .groups = "drop"
    ) %>%
    arrange(desc(化合物数量))
  
  print(kable(phase_summary))
} else {
  cat("未找到其他具有Molecular分类的相似化合物\n")
}

# 总结
cat("\nMolecular分析完成\n")
total_compounds <- nrow(substructure_data)
therapeutic_count <- sum(substructure_data$therapeutic_flag == TRUE, na.rm = TRUE)
cat("分析了", total_compounds, "个化合物, 其中", therapeutic_count, "个为治疗用药物\n")
