# ChEMBL药物查询和ATC分类分析工具

基于webchem包的通用药物分析工具，可以查询任意ChEMBL药物并进行亚结构搜索和ATC分类分析。

## 功能特点

1. **使用webchem包查询药物信息**：获取分子式、分子量、SMILES结构等基本信息
2. **Molecular分类信息**：分子类型、治疗标志、临床阶段、给药途径等
3. **亚结构搜索**：查找与目标药物结构相似的化合物
4. **ATC分类分析**：根据WHO ATC分类系统进行药理机制分析
5. **Molecular分类统计**：按分子类型和治疗用途进行统计分析
6. **多维度统计**：按ATC分类和molecular分类层级进行统计汇总

## 文件说明

- `chembl_drug_analyzer.R`：主要分析脚本（推荐使用）
- `final_webchem_chembl_analysis.R`：交互式版本脚本
- `CHEMBL25.xml`、`atc_class.xml`：本地数据文件（备用）

## 使用方法

### 方法1：命令行使用（推荐）

```bash
# 分析阿司匹林
Rscript chembl_drug_analyzer.R CHEMBL25

# 分析布洛芬
Rscript chembl_drug_analyzer.R CHEMBL521

# 使用默认药物（阿司匹林）
Rscript chembl_drug_analyzer.R
```

### 方法2：交互式使用

```bash
Rscript final_webchem_chembl_analysis.R
# 然后按提示输入ChEMBL ID
```

## 输出结果说明

### 1. 药物基本信息
- ChEMBL ID：数据库唯一标识符
- 药物名称：通用名称
- 分子式和分子量：化学结构信息
- 分子类型：Small molecule、Protein等
- 治疗标志：是否为治疗用药物
- 最大临床阶段：药物开发阶段
- 口服给药：给药途径信息
- 适应症分类：治疗领域
- ATC代码：WHO分类代码

### 2. 亚结构搜索结果
- 找到的相似化合物数量
- 相似化合物的基本信息

### 3. ATC分类分析
- 目标药物的详细ATC分类
- 相似化合物的ATC分类
- 按ATC一级分类的统计汇总

### 4. Molecular分类分析
- 按分子类型统计：Small molecule、Protein等
- 按治疗标志统计：治疗用vs非治疗用化合物

## 示例输出

```
ChEMBL药物分析工具
目标药物: CHEMBL521

1. 查询药物信息
ChEMBL ID: CHEMBL521
药物名称: IBUPROFEN
分子式: C13H18O2
分子量: 206.28
分子类型: Small molecule
治疗标志: 是
最大临床阶段: 4.0
口服给药: 是
适应症分类: Anti-Inflammatory
ATC代码: G02CC01, M01AE51, M02AA13, R02AX02, C01EB16, M01AE01

2. 获取ATC分类数据
获取 5369 条ATC分类记录

3. 亚结构搜索
找到 62 个相似化合物

4. ATC分类分析
匹配 8 条ATC记录

IBUPROFEN的ATC分类:
|ATC代码 |WHO名称                 |四级分类                    |一级分类                |
|M01AE01 |ibuprofen               |Propionic acid derivatives  |MUSCULO-SKELETAL SYSTEM|
...

按分子类型统计:
|mol_type       | 化合物数量|代表性化合物                    |
|Small molecule |         11|IBUPROFEN; LOXOPROFEN; ...      |

按治疗标志统计:
|therapeutic | 化合物数量|代表性化合物              |
|治疗用      |          3|IBUPROFEN; DEXIBUPROFEN; ...|
|非治疗用    |          8|LOXOPROFEN; ...             |

分析完成
分析了 3 个化合物, 8 个ATC代码
```

## 常用ChEMBL ID

- CHEMBL25：阿司匹林（Aspirin）
- CHEMBL521：布洛芬（Ibuprofen）
- CHEMBL112：对乙酰氨基酚（Paracetamol）
- CHEMBL1200766：美托洛尔（Metoprolol）

## 依赖包

脚本会自动安装以下R包：
- webchem：ChEMBL数据查询
- httr：HTTP请求
- jsonlite：JSON数据处理
- dplyr、tidyr、purrr：数据处理
- knitr：表格输出

## 注意事项

1. 需要网络连接以访问ChEMBL API
2. 首次运行可能需要安装依赖包
3. 某些药物可能没有ATC分类信息
4. 亚结构搜索结果数量限制为200个化合物

## 技术特点

- **优先使用webchem包**：确保数据获取的标准化和可靠性
- **在线数据获取**：实时获取最新的ChEMBL和ATC数据
- **错误处理**：对网络请求和数据处理进行错误检查
- **灵活输入**：支持命令行参数和交互式输入
- **简洁输出**：提供清晰易懂的分析结果
