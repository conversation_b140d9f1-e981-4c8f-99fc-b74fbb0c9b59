# ==============================================================================
# 脚本目的: 使用ChEMBL数据对阿司匹林的亚结构进行ATC分类 (最终运行版)
#
# 描述:
# 1. 通过API从ChEMBL实时加载与阿司匹林(CHEMBL25)共享亚结构的分子。
# 2. 通过API加载ChEMBL完整的ATC分类数据。
# 3. 基于API返回的真实数据结构进行解析，根据ATC代码进行分类并显示结果。
#
# 作者: Gemini
# 日期: 2025-08-14
# ==============================================================================


# ------------------------------------------------------------------------------
# 步骤 1: 安装和加载所需的库
# ------------------------------------------------------------------------------
if (!requireNamespace("httr", quietly = TRUE)) install.packages("httr")
if (!requireNamespace("jsonlite", quietly = TRUE)) install.packages("jsonlite")
if (!requireNamespace("dplyr", quietly = TRUE)) install.packages("dplyr")
if (!requireNamespace("tidyr", quietly = TRUE)) install.packages("tidyr")
if (!requireNamespace("purrr", quietly = TRUE)) install.packages("purrr")
if (!requireNamespace("knitr", quietly = TRUE)) install.packages("knitr")

library(httr)
library(jsonlite)
library(dplyr)
library(tidyr)
library(purrr)
library(knitr)

# ------------------------------------------------------------------------------
# 步骤 2: 从ChEMBL API实时获取数据
# ------------------------------------------------------------------------------
cat("正在从ChEMBL API获取亚结构搜索结果...\n")
substructure_url <- "https://www.ebi.ac.uk/chembl/api/data/substructure/CHEMBL25.json?limit=200"
response_sub <- GET(substructure_url)

if (http_status(response_sub)$category != "Success") {
  stop("无法从ChEMBL API获取亚结构数据。请检查网络连接或API状态。")
}
sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"), flatten = TRUE)

cat("正在从ChEMBL API获取完整的ATC分类数据...\n")
atc_url <- "https://www.ebi.ac.uk/chembl/api/data/atc_class.json?limit=10000"
response_atc <- GET(atc_url)

if (http_status(response_atc)$category != "Success") {
  stop("无法从ChEMBL API获取ATC数据。请检查网络连接或API状态。")
}
atc_list <- fromJSON(content(response_atc, "text", encoding = "UTF-8"), flatten = TRUE)

cat("数据获取完成。\n\n")


# ------------------------------------------------------------------------------
# 步骤 3: 解析和处理数据 (最终修正)
# ------------------------------------------------------------------------------
cat("正在解析API返回的数据...\n")

# 定义一个安全的函数来提取level5代码
safe_extract_level5 <- function(classification_data) {
  if (is.data.frame(classification_data) && nrow(classification_data) > 0) {
    return(classification_data$level5)
  } else {
    return(NULL)
  }
}

# --- 解析亚结构数据 ---
substructure_data <- as_tibble(sub_list$molecules) %>%
  select(
    chembl_id = molecule_chembl_id,
    pref_name,
    atc_classifications
  ) %>%
  mutate(atc_codes = map(atc_classifications, safe_extract_level5)) %>%
  select(-atc_classifications)

# --- 创建ATC查找表 (已修正) ---
# **这是修正的核心部分**
# ChEMBL API返回的ATC代码在名为'id'的列中
atc_lookup <- as_tibble(atc_list$atc_classes) %>%
  rename(atc_code = id) # 使用正确的列名 'id'

cat("数据解析完成。\n\n")


# ------------------------------------------------------------------------------
# 步骤 4: 根据ATC机制进行分类和合并
# ------------------------------------------------------------------------------
cat("正在根据ATC代码对分子进行分类...\n")

classified_molecules <- substructure_data %>%
  unnest(atc_codes) %>%
  rename(atc_code = atc_codes)

final_results <- left_join(classified_molecules, atc_lookup, by = "atc_code")

aspirin_info <- final_results %>% filter(chembl_id == "CHEMBL25")

cat("分类完成。\n\n")


# ------------------------------------------------------------------------------
# 步骤 5: 输出结果
# ------------------------------------------------------------------------------
cat("--- 阿司匹林(CHEMBL25)的ATC分类 ---\n")
# (已修正) 使用正确的列名 'level4_description'
print(kable(select(aspirin_info, chembl_id, pref_name, atc_code, who_name, level4_description),
            caption = "阿司匹林(CHEMBL25)的ATC分类信息"))
cat("\n\n")


cat("--- 与阿司匹林具有相似亚结构且已分类的其他分子 ---\n")
output_table <- final_results %>%
  filter(chembl_id != "CHEMBL25") %>%
  select(
    `ChEMBL ID` = chembl_id,
    `Preferred Name` = pref_name,
    `ATC Code` = atc_code,
    `WHO Name` = who_name,
    `ATC Level 4` = level4_description
  ) %>%
  distinct() %>%
  mutate(across(everything(), ~replace_na(., "N/A")))

if (nrow(output_table) > 0) {
  print(kable(output_table, caption = "基于ATC分类的阿司匹林亚结构搜索结果"))
} else {
  cat("在亚结构搜索结果中未找到其他具有ATC分类的分子。\n")
}

cat("\n--- 脚本执行完毕 ---\n")