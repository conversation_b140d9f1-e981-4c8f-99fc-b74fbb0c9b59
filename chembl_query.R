# ==============================================================================
# 脚本目的: 使用webchem包和ChEMBL API对阿司匹林进行亚结构搜索和ATC分类
#
# 描述:
# 1. 使用webchem包的chembl_query函数查询指定的药物(阿司匹林 CHEMBL25)
# 2. 使用ChEMBL API进行亚结构搜索，查找与阿司匹林结构相似的化合物
# 3. 使用webchem包的chembl_atc_classes函数获取ATC分类数据
# 4. 根据ATC机制进行分类处理并输出结果
#
# 作者: AI Assistant
# 日期: 2025-08-14
# ==============================================================================


# ------------------------------------------------------------------------------
# 步骤 1: 安装和加载所需的库
# ------------------------------------------------------------------------------
# 安装webchem包（如果未安装）
if (!requireNamespace("webchem", quietly = TRUE)) install.packages("webchem")
if (!requireNamespace("httr", quietly = TRUE)) install.packages("httr")
if (!requireNamespace("jsonlite", quietly = TRUE)) install.packages("jsonlite")
if (!requireNamespace("dplyr", quietly = TRUE)) install.packages("dplyr")
if (!requireNamespace("tidyr", quietly = TRUE)) install.packages("tidyr")
if (!requireNamespace("purrr", quietly = TRUE)) install.packages("purrr")
if (!requireNamespace("knitr", quietly = TRUE)) install.packages("knitr")

library(webchem)
library(httr)
library(jsonlite)
library(dplyr)
library(tidyr)
library(purrr)
library(knitr)

# ------------------------------------------------------------------------------
# 步骤 2: 使用webchem包查询阿司匹林信息
# ------------------------------------------------------------------------------
cat("正在使用webchem包查询阿司匹林(CHEMBL25)信息...\n")

# 使用webchem的chembl_query函数查询阿司匹林
aspirin_data <- chembl_query("CHEMBL25", resource = "molecule")
cat("阿司匹林基本信息查询完成。\n")

# 使用webchem获取ATC分类数据
cat("正在使用webchem获取ATC分类数据...\n")
atc_data <- chembl_atc_classes()
cat("ATC分类数据获取完成。\n")

# ------------------------------------------------------------------------------
# 步骤 3: 使用ChEMBL API进行亚结构搜索（webchem暂不支持此功能）
# ------------------------------------------------------------------------------
cat("正在从ChEMBL API获取亚结构搜索结果...\n")
substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                          "substructure/CHEMBL25.json?limit=200")
response_sub <- GET(substructure_url)

if (http_status(response_sub)$category != "Success") {
  stop("无法从ChEMBL API获取亚结构数据。请检查网络连接或API状态。")
}
sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"),
                     flatten = TRUE)

cat("亚结构搜索完成。\n\n")


# ------------------------------------------------------------------------------
# 步骤 4: 解析和处理数据
# ------------------------------------------------------------------------------
cat("正在解析数据...\n")

# 定义一个安全的函数来提取level5代码
safe_extract_level5 <- function(classification_data) {
  if (is.data.frame(classification_data) && nrow(classification_data) > 0) {
    classification_data$level5
  } else {
    NULL
  }
}

# --- 解析亚结构搜索数据 ---
substructure_data <- as_tibble(sub_list$molecules) %>%
  select(
    chembl_id = molecule_chembl_id,
    pref_name,
    atc_classifications
  ) %>%
  mutate(atc_codes = map(atc_classifications, safe_extract_level5)) %>%
  select(-atc_classifications)

# --- 处理webchem获取的ATC数据 ---
# webchem的chembl_atc_classes返回的数据结构
if (is.data.frame(atc_data)) {
  atc_lookup <- atc_data %>%
    rename(atc_code = level5) %>%
    select(atc_code, who_name, level4_description, level1_description)
} else {
  # 如果webchem返回列表，需要转换
  atc_lookup <- as_tibble(atc_data) %>%
    rename(atc_code = level5) %>%
    select(atc_code, who_name, level4_description, level1_description)
}

cat("数据解析完成。\n\n")


# ------------------------------------------------------------------------------
# 步骤 5: 根据ATC机制进行分类和合并
# ------------------------------------------------------------------------------
cat("正在根据ATC代码对分子进行分类...\n")

# 处理亚结构搜索结果
classified_molecules <- substructure_data %>%
  unnest(atc_codes) %>%
  rename(atc_code = atc_codes)

final_results <- left_join(classified_molecules, atc_lookup, by = "atc_code")

# 从webchem查询结果中提取阿司匹林的ATC信息
aspirin_atc_codes <- aspirin_data[[1]]$atc_classifications
if (!is.null(aspirin_atc_codes) && length(aspirin_atc_codes) > 0) {
  aspirin_info_webchem <- data.frame(
    chembl_id = "CHEMBL25",
    pref_name = aspirin_data[[1]]$pref_name,
    atc_code = aspirin_atc_codes,
    stringsAsFactors = FALSE
  ) %>%
    left_join(atc_lookup, by = "atc_code")
} else {
  aspirin_info_webchem <- data.frame()
}

# 从亚结构搜索结果中获取阿司匹林信息
aspirin_info_substructure <- final_results %>%
  filter(chembl_id == "CHEMBL25")

cat("分类完成。\n\n")


# ------------------------------------------------------------------------------
# 步骤 6: 输出结果
# ------------------------------------------------------------------------------
cat("=== 使用webchem包查询的阿司匹林信息 ===\n")
if (nrow(aspirin_info_webchem) > 0) {
  print(kable(aspirin_info_webchem %>%
                select(chembl_id, pref_name, atc_code, who_name,
                       level4_description),
                caption = "阿司匹林(CHEMBL25)的ATC分类信息 - webchem查询"))
} else {
  cat("webchem查询未返回阿司匹林的ATC分类信息。\n")
}
cat("\n\n")


cat("=== 亚结构搜索结果中的阿司匹林信息 ===\n")
if (nrow(aspirin_info_substructure) > 0) {
  print(kable(aspirin_info_substructure %>%
                select(chembl_id, pref_name, atc_code, who_name,
                       level4_description),
              caption = "阿司匹林(CHEMBL25)的ATC分类信息 - 亚结构搜索"))
} else {
  cat("亚结构搜索结果中未找到阿司匹林的ATC分类信息。\n")
}
cat("\n\n")

cat("=== 与阿司匹林具有相似亚结构且已分类的其他分子 ===\n")
output_table <- final_results %>%
  filter(chembl_id != "CHEMBL25") %>%
  select(
    `ChEMBL ID` = chembl_id,
    `Preferred Name` = pref_name,
    `ATC Code` = atc_code,
    `WHO Name` = who_name,
    `ATC Level 4` = level4_description,
    `ATC Level 1` = level1_description
  ) %>%
  distinct() %>%
  mutate(across(everything(), ~replace_na(., "N/A")))

if (nrow(output_table) > 0) {
  print(kable(output_table,
              caption = "基于ATC分类的阿司匹林亚结构搜索结果"))

  # 按ATC Level 1进行分类统计
  cat("\n=== 按ATC一级分类统计 ===\n")
  atc_summary <- output_table %>%
    filter(`ATC Level 1` != "N/A") %>%
    group_by(`ATC Level 1`) %>%
    summarise(
      `化合物数量` = n(),
      `代表性化合物` = paste(head(`Preferred Name`, 3), collapse = ", "),
      .groups = "drop"
    )

  if (nrow(atc_summary) > 0) {
    print(kable(atc_summary, caption = "按ATC一级分类的统计结果"))
  }
} else {
  cat("在亚结构搜索结果中未找到其他具有ATC分类的分子。\n")
}

cat("\n=== 脚本执行完毕 ===\n")
cat("总结:\n")
cat("1. 使用webchem包成功查询了阿司匹林(CHEMBL25)的基本信息\n")
cat("2. 使用ChEMBL API进行了亚结构搜索\n")
cat("3. 使用webchem包获取了完整的ATC分类数据\n")
cat("4. 根据ATC机制对搜索结果进行了分类和统计\n")