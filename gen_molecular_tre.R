# 基于Molecular分类生成分类树
library(data.tree)
library(dplyr)
library(webchem)
library(httr)
library(jsonlite)
library(tidyr)
library(purrr)

cat("生成Molecular分类树\n")

# 获取一些代表性药物进行分析
representative_drugs <- c("CHEMBL25", "CHEMBL521", "CHEMBL112", "CHEMBL1200766", 
                         "CHEMBL154", "CHEMBL1082", "CHEMBL6")

cat("分析", length(representative_drugs), "个代表性药物的molecular特性...\n")

# 收集molecular分类数据
molecular_data <- data.frame()

for (drug_id in representative_drugs) {
  cat("查询", drug_id, "...\n")
  
  # 查询药物信息
  drug_data <- chembl_query(drug_id, resource = "molecule")
  
  if (length(drug_data) > 0 && !is.null(drug_data[[1]])) {
    drug_info <- drug_data[[1]]
    
    # 进行亚结构搜索获取更多相似化合物
    substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                              "substructure/", drug_id, ".json?limit=50")
    
    response_sub <- GET(substructure_url)
    
    if (http_status(response_sub)$category == "Success") {
      sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"), 
                           flatten = TRUE)
      
      if (!is.null(sub_list$molecules) && length(sub_list$molecules) > 0) {
        sub_data <- as_tibble(sub_list$molecules) %>%
          select(chembl_id = molecule_chembl_id, pref_name, 
                 molecule_type, therapeutic_flag, max_phase, oral, indication_class) %>%
          filter(!is.na(pref_name))
        
        molecular_data <- rbind(molecular_data, sub_data)
      }
    }
  }
  
  Sys.sleep(0.5)  # 避免API请求过快
}

# 去重
molecular_data <- molecular_data %>% distinct()

cat("收集到", nrow(molecular_data), "个化合物的molecular数据\n")

# 构建Molecular层级结构的边列表
cat("构建Molecular层级结构...\n")

edge_list <- data.frame(child = character(), parent = character(), stringsAsFactors = FALSE)

for (i in 1:nrow(molecular_data)) {
  row <- molecular_data[i, ]
  
  # 处理缺失值
  mol_type <- ifelse(is.na(row$molecule_type) | row$molecule_type == "", "Unknown_Type", row$molecule_type)
  therapeutic <- ifelse(is.na(row$therapeutic_flag) | row$therapeutic_flag == "", "Unknown_Therapeutic", 
                       ifelse(row$therapeutic_flag, "Therapeutic", "Non_Therapeutic"))
  phase <- ifelse(is.na(row$max_phase), "Unknown_Phase", paste0("Phase_", row$max_phase))
  oral <- ifelse(is.na(row$oral) | row$oral == "", "Unknown_Route", 
                ifelse(row$oral, "Oral", "Non_Oral"))
  indication <- ifelse(is.na(row$indication_class) | row$indication_class == "", "Unknown_Indication", 
                      gsub("[;,]", "_", row$indication_class))
  
  drug_name <- paste0(row$pref_name, " (", row$chembl_id, ")")
  
  # Drug -> Indication
  if (indication != "Unknown_Indication") {
    edge_list <- rbind(edge_list, data.frame(
      child = drug_name,
      parent = paste0("Indication_", indication),
      stringsAsFactors = FALSE
    ))
    
    # Indication -> Therapeutic
    edge_list <- rbind(edge_list, data.frame(
      child = paste0("Indication_", indication),
      parent = paste0("Therapeutic_", therapeutic),
      stringsAsFactors = FALSE
    ))
  } else {
    # Drug -> Therapeutic (直接连接)
    edge_list <- rbind(edge_list, data.frame(
      child = drug_name,
      parent = paste0("Therapeutic_", therapeutic),
      stringsAsFactors = FALSE
    ))
  }
  
  # Therapeutic -> Phase
  edge_list <- rbind(edge_list, data.frame(
    child = paste0("Therapeutic_", therapeutic),
    parent = paste0("Clinical_", phase),
    stringsAsFactors = FALSE
  ))
  
  # Phase -> Route
  edge_list <- rbind(edge_list, data.frame(
    child = paste0("Clinical_", phase),
    parent = paste0("Route_", oral),
    stringsAsFactors = FALSE
  ))
  
  # Route -> Molecule Type
  edge_list <- rbind(edge_list, data.frame(
    child = paste0("Route_", oral),
    parent = paste0("MolType_", mol_type),
    stringsAsFactors = FALSE
  ))
  
  # Molecule Type -> Root
  edge_list <- rbind(edge_list, data.frame(
    child = paste0("MolType_", mol_type),
    parent = "Molecular_Classification_Root",
    stringsAsFactors = FALSE
  ))
}

# 去重
edge_list <- edge_list %>% distinct()

cat("生成", nrow(edge_list), "条边关系\n")

# 构建树形结构
cat("构建Molecular分类树...\n")
tree_df <- edge_list %>%
  select(parent, child) %>%
  rename(from = parent, to = child) %>%
  distinct()

# 构建树
molecular_tree <- FromDataFrameNetwork(tree_df)

# 导出为Newick格式的函数
asNewick <- function(node) {
  if (node$isLeaf) {
    return(paste0("'", gsub("'", "''", node$name), "'"))
  } else {
    children_newick <- sapply(node$children, asNewick)
    return(paste0("(", paste(children_newick, collapse = ","), ")", "'", gsub("'", "''", node$name), "'"))
  }
}

# 生成Newick字符串
children_strings <- sapply(molecular_tree$children, asNewick)
newick_string <- paste0("(", paste(children_strings, collapse = ","), ");")

# 保存Molecular分类树文件
output_file <- "molecular_classification.tre"
write(newick_string, file = output_file)

cat("Molecular分类树已保存到:", output_file, "\n")

# 创建查询函数
GetMolecularClassificationPath <- function(tree, drug_name) {
  # 在树中查找包含药物名称的节点
  found_nodes <- Traverse(tree, filterFun = function(node) {
    grepl(drug_name, node$name, ignore.case = TRUE)
  })
  
  if (length(found_nodes) > 0) {
    for (node in found_nodes) {
      path_vector <- node$path
      path_string <- paste(path_vector, collapse = " -> ")
      
      cat(paste("药物 '", drug_name, "' 的Molecular分类路径:\n", sep = ""))
      cat(path_string, "\n\n")
    }
  } else {
    cat(paste("未在Molecular分类树中找到药物 '", drug_name, "'\n", sep = ""))
  }
}

# 示例查询
cat("\n=== Molecular分类路径查询示例 ===\n")

# 查询阿司匹林
GetMolecularClassificationPath(molecular_tree, "ASPIRIN")

# 查询布洛芬
GetMolecularClassificationPath(molecular_tree, "IBUPROFEN")

cat("Molecular分类树生成完成！\n")
