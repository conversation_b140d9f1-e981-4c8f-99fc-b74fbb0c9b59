# ==============================================================================
# 脚本名称: 基于webchem包的ChEMBL药物查询和ATC分类分析
#
# 功能描述:
# 1. 使用webchem包的chembl_query查询指定药物(阿司匹林 CHEMBL25)
# 2. 使用ChEMBL API进行亚结构搜索，查找与阿司匹林结构相似的化合物
# 3. 使用webchem包的chembl_atc_classes获取ATC分类数据
# 4. 根据ATC机制进行分类处理并输出详细的分析结果
#
# 主要特点:
# - 优先使用webchem包进行在线数据获取
# - 结合ChEMBL API实现亚结构搜索功能
# - 提供详细的ATC分类统计和分析
# - 生成易于理解的表格输出
#
# 作者: AI Assistant
# 日期: 2025-08-14
# ==============================================================================

# ------------------------------------------------------------------------------
# 步骤 1: 环境准备和包加载
# ------------------------------------------------------------------------------
cat("ChEMBL药物查询和ATC分类分析\n")
cat("加载必要的包...\n")

# 检查并安装必要的包
required_packages <- c("webchem", "httr", "jsonlite", "dplyr",
                      "tidyr", "purrr", "knitr")

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg, quiet = TRUE)
  }
}

# 加载库
suppressPackageStartupMessages({
  library(webchem)
  library(httr)
  library(jsonlite)
  library(dplyr)
  library(tidyr)
  library(purrr)
  library(knitr)
})

cat("包加载完成\n\n")

# ------------------------------------------------------------------------------
# 步骤 2: 查询阿司匹林信息
# ------------------------------------------------------------------------------
cat("1. 查询阿司匹林信息\n")

target_drug <- "CHEMBL25"
aspirin_data <- chembl_query(target_drug, resource = "molecule")

if (length(aspirin_data) > 0 && !is.null(aspirin_data[[1]])) {
  aspirin_info <- aspirin_data[[1]]

  # 基本信息
  cat("ChEMBL ID:", aspirin_info$molecule_chembl_id, "\n")
  cat("药物名称:", aspirin_info$pref_name, "\n")
  cat("分子式:", aspirin_info$molecule_properties$full_molformula, "\n")
  cat("分子量:", aspirin_info$molecule_properties$full_mwt, "\n")
  cat("SMILES:", aspirin_info$molecule_structures$canonical_smiles, "\n")

  if (length(aspirin_info$atc_classifications) > 0) {
    cat("ATC代码:", paste(aspirin_info$atc_classifications, collapse = ", "), "\n")
  }
} else {
  stop("无法获取阿司匹林信息")
}

cat("\n")

# ------------------------------------------------------------------------------
# 步骤 3: 获取ATC分类数据
# ------------------------------------------------------------------------------
cat("2. 获取ATC分类数据\n")

atc_data <- chembl_atc_classes()

if (length(atc_data) > 0) {
  cat("获取", nrow(atc_data), "条ATC分类记录\n\n")
} else {
  stop("无法获取ATC分类数据")
}

cat("\n")

# ------------------------------------------------------------------------------
# 步骤 4: 亚结构搜索
# ------------------------------------------------------------------------------
cat("3. 亚结构搜索\n")

substructure_url <- paste0("https://www.ebi.ac.uk/chembl/api/data/",
                          "substructure/", target_drug, ".json?limit=200")

response_sub <- GET(substructure_url)

if (http_status(response_sub)$category == "Success") {
  sub_list <- fromJSON(content(response_sub, "text", encoding = "UTF-8"),
                       flatten = TRUE)

  if (!is.null(sub_list$molecules) && length(sub_list$molecules) > 0) {
    cat("找到", length(sub_list$molecules), "个相似化合物\n")

    substructure_data <- as_tibble(sub_list$molecules) %>%
      select(chembl_id = molecule_chembl_id, pref_name, atc_classifications)
  } else {
    stop("亚结构搜索无结果")
  }
} else {
  stop("亚结构搜索失败")
}

cat("\n")

# ------------------------------------------------------------------------------
# 步骤 5: ATC分类分析
# ------------------------------------------------------------------------------
cat("4. ATC分类分析\n")

# 提取ATC代码
extract_atc_codes <- function(atc_classifications) {
  if (is.character(atc_classifications) && length(atc_classifications) > 0) {
    atc_classifications
  } else {
    NULL
  }
}

molecules_with_atc <- substructure_data %>%
  mutate(atc_codes = map(atc_classifications, extract_atc_codes)) %>%
  filter(!map_lgl(atc_codes, is.null)) %>%
  unnest(atc_codes) %>%
  rename(atc_code = atc_codes)

if (nrow(molecules_with_atc) > 0) {
  final_results <- molecules_with_atc %>%
    left_join(atc_data %>% rename(atc_code = level5), by = "atc_code") %>%
    filter(!is.na(who_name))

  if (nrow(final_results) > 0) {
    cat("匹配", nrow(final_results), "条ATC记录\n\n")

    # 阿司匹林ATC分类
    target_atc <- final_results %>% filter(chembl_id == target_drug)

    if (nrow(target_atc) > 0) {
      cat("阿司匹林ATC分类:\n")
      target_table <- target_atc %>%
        select(ATC代码 = atc_code, WHO名称 = who_name,
               四级分类 = level4_description, 一级分类 = level1_description)

      print(kable(target_table))
    }

    # 其他相似化合物
    other_compounds <- final_results %>%
      filter(chembl_id != target_drug) %>%
      select(chembl_id, pref_name, atc_code, who_name,
             level4_description, level1_description) %>%
      distinct() %>%
      mutate(pref_name = ifelse(is.na(pref_name), "未命名", pref_name))

    if (nrow(other_compounds) > 0) {
      cat("\n相似化合物ATC分类:\n")
      other_table <- other_compounds %>%
        select(ChEMBL_ID = chembl_id, 化合物名称 = pref_name,
               ATC代码 = atc_code, WHO名称 = who_name)

      print(kable(other_table))

      # 统计
      cat("\n按ATC一级分类统计:\n")
      atc_summary <- other_compounds %>%
        filter(!is.na(level1_description)) %>%
        group_by(level1_description) %>%
        summarise(
          化合物数量 = n(),
          代表性化合物 = paste(head(unique(pref_name), 3), collapse = "; "),
          .groups = "drop"
        ) %>%
        arrange(desc(化合物数量))

      print(kable(atc_summary))
    } else {
      cat("未找到其他具有ATC分类的相似化合物\n")
    }
  } else {
    cat("未匹配到ATC分类信息\n")
  }
} else {
  cat("搜索结果中无ATC分类\n")
}

# ------------------------------------------------------------------------------
# 步骤 6: 分析总结
# ------------------------------------------------------------------------------
cat("\n=== 分析总结报告 ===\n")
cat("✓ 使用webchem包成功查询了目标药物的基本信息\n")
cat("✓ 使用webchem包获取了完整的ATC分类数据库\n")
cat("✓ 使用ChEMBL API进行了亚结构搜索\n")
cat("✓ 完成了基于ATC机制的分类分析和统计\n")

if (exists("final_results") && nrow(final_results) > 0) {
  total_compounds <- length(unique(final_results$chembl_id))
  total_atc_codes <- length(unique(final_results$atc_code))
  cat(paste("✓ 总共分析了", total_compounds, "个化合物\n"))
  cat(paste("✓ 涉及", total_atc_codes, "个不同的ATC分类代码\n"))
}

cat("\n=== 脚本执行完毕 ===\n")
cat("感谢使用基于webchem包的ChEMBL药物分析工具！\n")
